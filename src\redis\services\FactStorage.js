import { getRedisClient } from '../redisClient.js';
import { FACT_REFINEMENT_PROMPT } from '../../constants.js';
import { callGenerativeAI } from '../../chat/geminiAI.js';

export class FactStorage {
	constructor(env) {
		this.env = env;
		this.redis = getRedisClient(env);
	}

	async storeFacts(userId, facts) {
		if (!facts.length) return 0;

		const factsKey = `user:${userId}:facts`;
		const existingFacts = await this.redis.smembers(factsKey);
		const refinedFacts = await this._refineFacts(existingFacts, facts);

		await this._atomicStore(factsKey, refinedFacts);
		console.log(`[FactStorage] Stored ${refinedFacts.length} facts for user ${userId}`);
		return refinedFacts.length;
	}

	async getUserFacts(userId) {
		const factsKey = `user:${userId}:facts`;
		try {
			return await this.redis.smembers(factsKey);
		} catch (error) {
			console.error(`[FactStorage] Error getting facts for user ${userId}:`, error);
			return [];
		}
	}

	async _refineFacts(existingFacts, newFacts) {
		const allFacts = [...new Set([...existingFacts, ...newFacts])];

		if (existingFacts.length < 1 || newFacts.length < 1) return allFacts;

		const userPrompt = `<INSTRUCTIONS>Merge and deduplicate these facts</INSTRUCTIONS>\n<EXISTING_FACTS>\n${existingFacts.join(
			'\n'
		)}</EXISTING_FACTS>\n\n<NEW_FACTS>\n${newFacts.join('\n')}</NEW_FACTS>`;

		const temperature = 0.1;
		const systemPrompt = FACT_REFINEMENT_PROMPT;

		// Use Gemini
		const config = {
			temperature: temperature,
			systemInstruction: systemPrompt,
			thinkingBudget: 0,
			model: 'gemini-2.5-flash-lite-preview-06-17',
			traceTags: ['fact-refine'],
		};

		const contents = [{ role: 'user', parts: [{ text: userPrompt }] }];
		const response = await callGenerativeAI(this.env, config, contents);

		if (!response) return allFacts;

		const responseText = this._extractResponseText(response.text);

		return responseText
			.split('\n')
			.map((fact) => fact.replace(/^[-•*]\s*/, ''))
			.filter((fact) => fact.length > 5);
	}

	_extractResponseText(response) {
		if (!response) return '';

		if (Array.isArray(response)) {
			const last = response[response.length - 1];
			return last?.text.trim() || '';
		}

		return typeof response === 'string' ? response.trim() : '';
	}

	async _atomicStore(factsKey, facts) {
		const pipeline = this.redis.pipeline();
		pipeline.del(factsKey);
		if (facts.length > 0) {
			pipeline.sadd(factsKey, ...facts);
		}
		await pipeline.exec();
	}
}
