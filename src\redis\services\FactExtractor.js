import { callGenerativeAI } from '../../chat/geminiAI.js';
import { formatRedisHistory } from '../../chat/history/historyManager.js';

export class FactExtractor {
	constructor(env) {
		this.env = env;
	}

	async extractFacts(firstName, text, messageHistory) {
		const { EXTRACTION_PROMPT, EXTRACTION_SYSTEM_PROMPT } = await import('../../constants.js');

		const context = formatRedisHistory(messageHistory, this.env);
		const userMessage = this._formatUserMessage(firstName, text);
		const prompt = EXTRACTION_PROMPT.replace('{USER_MESSAGE}', userMessage).replace('{HISTORY}', context);

		const response = await this._callAI(prompt, EXTRACTION_SYSTEM_PROMPT);
		const responseText = this._extractResponseText(response.text);

		return this._parseFacts(responseText, firstName);
	}

	_formatUserMessage(firstName, text) {
		const timestamp = new Date().toLocaleString('en-US', { timeZone: this.env.TIMEZONE });
		return `<CURRENT_DATE>${timestamp}</CURRENT_DATE>\n<USER_NAME>${firstName}</USER_NAME>\n<USER_MESSAGE>${text}</USER_MESSAGE>`;
	}

	async _callAI(prompt, systemPrompt) {
		const temperature = 0.1;

		// Use Gemini
		const config = {
			temperature: temperature,
			systemInstruction: systemPrompt,
			thinkingBudget: 0,
			model: 'gemini-2.5-flash-lite-preview-06-17',
			traceTags: ['fact-extract'],
		};

		const contents = [{ role: 'user', parts: [{ text: prompt }] }];
		return callGenerativeAI(this.env, config, contents);
	}

	_extractResponseText(response) {
		if (!response) return '';

		if (Array.isArray(response)) {
			const last = response[response.length - 1];
			return last?.text.trim() || '';
		}

		return typeof response === 'string' ? response.trim() : '';
	}

	_parseFacts(response, firstName) {
		if (!response) {
			console.warn(`[FactExtractor] No response from AI for user ${firstName}`);
			return [];
		}

		if (response.toUpperCase() === 'NO_FACTS_FOUND') {
			console.log(`[FactExtractor] No facts found for ${firstName}`);
			return [];
		}

		const facts = response
			.split('\n')
			.map((fact) => fact.replace(/^[-•*]\s*/, '').replace(/^\d+\.\s*/, ''))
			.filter((fact) => fact.length > 5);

		console.log(`[FactExtractor] Extracted ${facts.length} facts for ${firstName}`);
		return facts;
	}
}
